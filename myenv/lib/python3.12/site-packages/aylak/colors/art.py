a = """░█████╗░
██╔══██╗
███████║
██╔══██║
██║░░██║
╚═╝░░╚═╝"""


b = """██████╗░
██╔══██╗
██████╦╝
██╔══██╗
██████╦╝
╚═════╝░"""


c = """░█████╗░
██╔══██╗
██║░░╚═╝
██║░░██╗
╚█████╔╝
░╚════╝░"""

d = """██████╗░
██╔══██╗
██║░░██║
██║░░██║
██████╔╝
╚═════╝░"""


e = """███████╗
██╔════╝
█████╗░░
██╔══╝░░
███████╗
╚══════╝"""


f = """███████╗
██╔════╝
█████╗░░
██╔══╝░░
██║░░░░░
╚═╝░░░░░"""


g = """░██████╗░
██╔════╝░
██║░░██╗░
██║░░╚██╗
╚██████╔╝
░╚═════╝░"""


h = """██╗░░██╗
██║░░██║
███████║
██╔══██║
██║░░██║
╚═╝░░╚═╝"""


i = """██╗
██║
██║
██║
██║
╚═╝"""


j = """░░░░░██╗
░░░░░██║
░░░░░██║
██╗░░██║
╚█████╔╝
░╚════╝░"""


k = """██╗░░██╗
██║░██╔╝
█████═╝░
██╔═██╗░
██║░╚██╗
╚═╝░░╚═╝"""


l = """██╗░░░░░
██║░░░░░
██║░░░░░
██║░░░░░
███████╗
╚══════╝"""


m = """███╗░░░███╗
████╗░████║
██╔████╔██║
██║╚██╔╝██║
██║░╚═╝░██║
╚═╝░░░░░╚═╝"""


n = """███╗░░██╗
████╗░██║
██╔██╗██║
██║╚████║
██║░╚███║
╚═╝░░╚══╝"""


o = """░█████╗░
██╔══██╗
██║░░██║
██║░░██║
╚█████╔╝
░╚════╝░"""


p = """██████╗░
██╔══██╗
██████╔╝
██╔═══╝░
██║░░░░░
╚═╝░░░░░"""


q = """░██████╗░
██╔═══██╗
██║██╗██║
╚██████╔╝
░╚═██╔═╝░
░░░╚═╝░░░"""


r = """██████╗░
██╔══██╗
██████╔╝
██╔══██╗
██║░░██║
╚═╝░░╚═╝"""


s = """░██████╗
██╔════╝
╚█████╗░
░╚═══██╗
██████╔╝
╚═════╝░"""


t = """████████╗
╚══██╔══╝
░░░██║░░░
░░░██║░░░
░░░██║░░░
░░░╚═╝░░░"""


u = """██╗░░░██╗
██║░░░██║
██║░░░██║
██║░░░██║
╚██████╔╝
░╚═════╝░"""


v = """██╗░░░██╗
██║░░░██║
╚██╗░██╔╝
░╚████╔╝░
░░╚██╔╝░░
░░░╚═╝░░░"""


w = """░██╗░░░░░░░██╗
░██║░░██╗░░██║
░╚██╗████╗██╔╝
░░████╔═████║░
░░╚██╔╝░╚██╔╝░
░░░╚═╝░░░╚═╝░░"""


x = """██╗░░██╗
╚██╗██╔╝
░╚███╔╝░
░██╔██╗░
██╔╝╚██╗
╚═╝░░╚═╝"""


y = """██╗░░░██╗
╚██╗░██╔╝
░╚████╔╝░
░░╚██╔╝░░
░░░██║░░░
░░░╚═╝░░░"""


z = """███████╗
╚════██║
░░███╔═╝
██╔══╝░░
███████╗
╚══════╝"""


def big_text_art(text: str, print_it: bool = True):
    text = text.lower()
    text = (
        text.replace(" ", "")
        .replace("ı", "i")
        .replace("ç", "c")
        .replace("ş", "s")
        .replace("ğ", "g")
        .replace("ü", "u")
        .replace("ö", "o")
        .replace("İ", "I")
        .replace("Ç", "C")
        .replace("Ş", "S")
        .replace("Ğ", "G")
        .replace("Ü", "U")
        .replace("Ö", "O")
    )
    aylak = {
        "a": a,
        "b": b,
        "c": c,
        "d": d,
        "e": e,
        "f": f,
        "g": g,
        "h": h,
        "i": i,
        "j": j,
        "k": k,
        "l": l,
        "m": m,
        "n": n,
        "o": o,
        "p": p,
        "q": q,
        "r": r,
        "s": s,
        "t": t,
        "u": u,
        "v": v,
        "w": w,
        "x": x,
        "y": y,
        "z": z,
    }
    _ = ""
    for ii in range(6):
        line = ""
        for char in text:
            line += aylak[char].split("\n")[ii]
        if line:
            if print_it:
                print(line)
            else:
                _ += line + "\n"
    return _
