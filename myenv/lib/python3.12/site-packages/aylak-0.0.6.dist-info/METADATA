Metadata-Version: 2.2
Name: aylak
Version: 0.0.6
Summary: Aylak PyPi
Home-page: https://github.com/aylak-github/aylak-pypi
Author: aylak-github
Author-email: <EMAIL>
License: GNU AFFERO GENERAL PUBLIC LICENSE (v3)
Keywords: aylak,pypi,aylak-pypi,aylak-pypi
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Internet
Classifier: Topic :: Communications
Classifier: Topic :: Communications :: Chat
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: pythonansi==1.0.2
Requires-Dist: aiofiles==23.2.1
Requires-Dist: cryptography==42.0.5
Requires-Dist: pillow==10.2.0
Requires-Dist: aiohttp>=3.8.1
Requires-Dist: telethon
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
